//
//  CardModel+MYRecordConvertible.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import CloudKit
import Foundation
import MYCloudKit

/// CardModel 的 MYRecordConvertible 实现
///
/// 将 SwiftData 的 CardModel 适配到 MYCloudKit 同步系统
extension CardModel: MYRecordConvertible {

  /// CloudKit 记录的唯一标识符
  /// 使用 UUID 字符串作为唯一标识符
  public var myRecordID: String {
    return id.uuidString
  }

  /// CloudKit 记录类型
  public var myRecordType: String {
    return "Card"
  }

  /// 根组ID - 按照 Bad-Habits 的方式实现
  public var myRootGroupID: String? {
    switch CloudSyncManager.shared.shareType {
    case .recordWithMYZone:
      return nil
    case .zone, .recordWithCustomZone:
      return id.uuidString  // 每个卡片是独立的组，类似 Bad-Habits 中的 Problem
    }
  }

  /// 父记录ID - 卡片没有父记录
  public var myParentID: String? {
    return nil
  }

  /// 需要同步到 CloudKit 的属性
  public var myProperties: [String: MYRecordValue] {
    var properties: [String: MYRecordValue] = [
      "name": .string(name),
      "currency": .string(currency),
      "symbol": .string(symbol),
      "balance": .double(balance),
      "credit": .double(credit),
      "isCredit": .bool(isCredit),
      "isSelected": .bool(isSelected),
      "isStatistics": .bool(isStatistics),
      "order": .int64(Int64(order)),
      "remark": .string(remark),
      "cover": .string(cover),
      "bankName": .string(bankName),
      "cardNumber": .string(cardNumber),
      "isFixedDueDay": .bool(isFixedDueDay),
      "createdAt": .date(createdAt),
      "updatedAt": .date(updatedAt),
    ]

    // 可选字段处理
    if let billDay = billDay {
      properties["billDay"] = .int64(Int64(billDay))
    }

    if let dueDay = dueDay {
      properties["dueDay"] = .int64(Int64(dueDay))
    }

    // 银行Logo处理 - 如果有数据则作为资产上传
    if let bankLogo = bankLogo, !bankLogo.isEmpty {
      // 创建临时文件用于上传
      let tempURL = createTempFileForBankLogo(bankLogo)
      if let tempURL = tempURL {
        properties["bankLogo"] = .asset(tempURL)
      }
    }

    return properties
  }

  /// 为银行Logo创建临时文件
  /// - Parameter data: 图片数据
  /// - Returns: 临时文件URL
  private func createTempFileForBankLogo(_ data: Data) -> URL? {
    let tempDir = FileManager.default.temporaryDirectory
    let fileName = "bankLogo_\(id.uuidString).png"
    let tempURL = tempDir.appendingPathComponent(fileName)

    do {
      try data.write(to: tempURL)
      return tempURL
    } catch {
      print("❌ 创建银行Logo临时文件失败: \(error)")
      return nil
    }
  }
}

// MARK: - 便利方法
extension CardModel {

  /// 从 CloudKit 记录创建或更新 CardModel
  /// - Parameters:
  ///   - record: CloudKit 获取的记录
  ///   - existing: 现有的 CardModel 实例（可选）
  /// - Returns: 更新后的 CardModel
  static func from(record: MYSyncEngine.FetchedRecord, existing: CardModel? = nil) -> CardModel {
    let card = existing ?? CardModel()

    // 基本属性更新
    if let idString = record.id as String?, let uuid = UUID(uuidString: idString) {
      card.id = uuid
    }

    card.name = record.value(for: "name") ?? ""
    card.currency = record.value(for: "currency") ?? ""
    card.symbol = record.value(for: "symbol") ?? ""
    card.balance = record.value(for: "balance") ?? 0.0
    card.credit = record.value(for: "credit") ?? 0.0
    card.isCredit = record.value(for: "isCredit") ?? false
    card.isSelected = record.value(for: "isSelected") ?? true
    card.isStatistics = record.value(for: "isStatistics") ?? false
    card.order = Int(record.value(for: "order") as? Int64 ?? 0)
    card.remark = record.value(for: "remark") ?? ""
    card.cover = record.value(for: "cover") ?? ""
    card.bankName = record.value(for: "bankName") ?? ""
    card.cardNumber = record.value(for: "cardNumber") ?? ""
    card.isFixedDueDay = record.value(for: "isFixedDueDay") ?? true

    // 可选字段处理
    if let billDay = record.value(for: "billDay") as? Int64 {
      card.billDay = Int(billDay)
    }

    if let dueDay = record.value(for: "dueDay") as? Int64 {
      card.dueDay = Int(dueDay)
    }

    // 银行Logo处理 - 从CloudKit资产下载
    if let assetURL: URL = record.value(for: "bankLogo") {
      do {
        let data = try Data(contentsOf: assetURL)
        card.bankLogo = data
      } catch {
        print("❌ 下载银行Logo失败: \(error)")
      }
    }

    // 时间戳处理
    if let createdAt: Date = record.value(for: "createdAt") {
      card.createdAt = createdAt
    }
    if let updatedAt: Date = record.value(for: "updatedAt") {
      card.updatedAt = updatedAt
    } else {
      card.updatedAt = Date()
    }

    return card
  }

  /// 检查是否需要同步到 CloudKit
  /// - Returns: 如果需要同步返回 true
  func needsSync() -> Bool {
    // 这里可以添加逻辑来判断是否需要同步
    // 例如检查 updatedAt 时间戳等
    return true
  }

  /// 准备同步到 CloudKit
  /// 在同步前调用此方法来更新时间戳等
  func prepareForSync() {
    self.updatedAt = Date()
  }
}

// MARK: - 同步辅助方法
extension CardModel {

  /// 同步当前卡片到 CloudKit
  func syncToCloud() {
    prepareForSync()
    CloudSyncManager.shared.sync(self)
  }

  /// 从 CloudKit 删除当前卡片
  func deleteFromCloud() {
    CloudSyncManager.shared.delete(self)
  }

  /// 批量同步多个卡片
  /// - Parameter cards: 要同步的卡片数组
  static func syncMultipleToCloud(_ cards: [CardModel]) {
    for card in cards {
      card.syncToCloud()
    }
  }
}
