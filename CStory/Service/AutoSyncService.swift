//
//  AutoSyncService.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import SwiftData
import Foundation

/// 自动同步服务
/// 
/// 提供便捷的方法来自动触发 CloudKit 同步
@Observable
final class AutoSyncService {
    
    static let shared = AutoSyncService()
    
    private let syncManager = CloudSyncManager.shared
    
    private init() {}
    
    // MARK: - 自动同步方法
    
    /// 保存并同步货币
    func saveAndSync(_ currency: CurrencyModel, context: ModelContext) {
        context.insert(currency)
        
        do {
            try context.save()
            print("💾 货币已保存到本地")
            
            // 自动同步到 CloudKit
            currency.syncToCloud()
            print("☁️ 货币已提交同步")
        } catch {
            print("❌ 保存货币失败: \(error)")
        }
    }
    
    /// 保存并同步卡片
    func saveAndSync(_ card: CardModel, context: ModelContext) {
        context.insert(card)
        
        do {
            try context.save()
            print("💾 卡片已保存到本地")
            
            // 自动同步到 CloudKit
            card.syncToCloud()
            print("☁️ 卡片已提交同步")
        } catch {
            print("❌ 保存卡片失败: \(error)")
        }
    }
    
    /// 保存并同步交易
    func saveAndSync(_ transaction: TransactionModel, context: ModelContext) {
        context.insert(transaction)
        
        do {
            try context.save()
            print("💾 交易已保存到本地")
            
            // 自动同步到 CloudKit
            transaction.syncToCloud()
            print("☁️ 交易已提交同步")
        } catch {
            print("❌ 保存交易失败: \(error)")
        }
    }
    
    /// 保存并同步主分类
    func saveAndSync(_ category: TransactionMainCategoryModel, context: ModelContext) {
        context.insert(category)
        
        do {
            try context.save()
            print("💾 主分类已保存到本地")
            
            // 自动同步到 CloudKit
            category.syncToCloud()
            print("☁️ 主分类已提交同步")
        } catch {
            print("❌ 保存主分类失败: \(error)")
        }
    }
    
    /// 保存并同步子分类
    func saveAndSync(_ subCategory: TransactionSubCategoryModel, context: ModelContext) {
        context.insert(subCategory)
        
        do {
            try context.save()
            print("💾 子分类已保存到本地")
            
            // 自动同步到 CloudKit
            subCategory.syncToCloud()
            print("☁️ 子分类已提交同步")
        } catch {
            print("❌ 保存子分类失败: \(error)")
        }
    }
    
    /// 保存并同步聊天消息
    func saveAndSync(_ message: ChatMessageModel, context: ModelContext) {
        context.insert(message)
        
        do {
            try context.save()
            print("💾 聊天消息已保存到本地")
            
            // 自动同步到 CloudKit
            message.syncToCloud()
            print("☁️ 聊天消息已提交同步")
        } catch {
            print("❌ 保存聊天消息失败: \(error)")
        }
    }
    
    // MARK: - 更新并同步方法
    
    /// 更新并同步任意模型
    func updateAndSync<T: MYRecordConvertible>(_ model: T, context: ModelContext) {
        do {
            try context.save()
            print("💾 \(T.self) 已更新到本地")
            
            // 自动同步到 CloudKit
            syncManager.sync(model)
            print("☁️ \(T.self) 已提交同步")
        } catch {
            print("❌ 更新 \(T.self) 失败: \(error)")
        }
    }
    
    // MARK: - 删除并同步方法
    
    /// 删除并同步任意模型
    func deleteAndSync<T: MYRecordConvertible>(_ model: T, context: ModelContext) {
        // 先从 CloudKit 删除
        syncManager.delete(model)
        print("☁️ \(T.self) 已提交删除同步")
        
        // 再从本地删除
        context.delete(model as! any PersistentModel)
        
        do {
            try context.save()
            print("💾 \(T.self) 已从本地删除")
        } catch {
            print("❌ 删除 \(T.self) 失败: \(error)")
        }
    }
    
    // MARK: - 批量操作
    
    /// 批量保存并同步
    func batchSaveAndSync<T: MYRecordConvertible & PersistentModel>(_ models: [T], context: ModelContext) {
        for model in models {
            context.insert(model)
        }
        
        do {
            try context.save()
            print("💾 批量保存 \(models.count) 个 \(T.self) 到本地")
            
            // 批量同步到 CloudKit
            for model in models {
                syncManager.sync(model)
            }
            print("☁️ 批量提交 \(models.count) 个 \(T.self) 同步")
        } catch {
            print("❌ 批量保存 \(T.self) 失败: \(error)")
        }
    }
}
