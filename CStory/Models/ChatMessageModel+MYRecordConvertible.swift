//
//  ChatMessageModel+MYRecordConvertible.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import Foundation
import MYCloudKit
import CloudKit

/// ChatMessageModel 的 MYRecordConvertible 实现
extension ChatMessageModel: MYRecordConvertible {
    
    /// CloudKit 记录的唯一标识符
    public var myRecordID: String {
        return id.uuidString
    }
    
    /// CloudKit 记录类型
    public var myRecordType: String {
        return "ChatMessage"
    }
    
    /// 根组ID - 聊天消息可以按日期分组
    public var myRootGroupID: String? {
        switch CloudSyncManager.shared.shareType {
        case .recordWithMYZone:
            return nil
        case .zone, .recordWithCustomZone:
            // 按年月分组聊天消息
            let formatter = DateFormatter()
            formatter.dateFormat = "yyyy-MM"
            return "ChatGroup_\(formatter.string(from: timestamp))"
        }
    }
    
    /// 父记录ID - 聊天消息没有父记录
    public var myParentID: String? {
        return nil
    }
    
    /// 需要同步到 CloudKit 的属性
    public var myProperties: [String: MYRecordValue] {
        var properties: [String: MYRecordValue] = [
            "role": .string(role),
            "content": .string(content),
            "timestamp": .date(timestamp)
        ]
        
        // 处理关联的交易ID列表
        if let transactionIdsData = transactionIdsData,
           !transactionIdsData.isEmpty {
            // 将 Data 转换为 JSON 字符串存储
            if let jsonString = String(data: transactionIdsData, encoding: .utf8) {
                properties["transactionIdsData"] = .string(jsonString)
            }
        }
        
        // 处理图片数据数组
        if let imageDataArray = imageDataArray,
           !imageDataArray.isEmpty {
            // 创建临时文件用于上传图片数据
            if let tempURL = createTempFileForImageData(imageDataArray) {
                properties["imageDataArray"] = .asset(tempURL)
            }
        }
        
        return properties
    }
    
    /// 为图片数据创建临时文件
    /// - Parameter data: 图片数据数组的编码数据
    /// - Returns: 临时文件URL
    private func createTempFileForImageData(_ data: Data) -> URL? {
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = "chatImages_\(id.uuidString).data"
        let tempURL = tempDir.appendingPathComponent(fileName)
        
        do {
            try data.write(to: tempURL)
            return tempURL
        } catch {
            print("❌ 创建聊天图片临时文件失败: \(error)")
            return nil
        }
    }
}

// MARK: - 便利方法
extension ChatMessageModel {
    
    /// 从 CloudKit 记录创建或更新 ChatMessageModel
    /// - Parameters:
    ///   - record: CloudKit 获取的记录
    ///   - existing: 现有的实例（可选）
    /// - Returns: 更新后的实例
    static func from(record: MYSyncEngine.FetchedRecord, existing: ChatMessageModel? = nil) -> ChatMessageModel {
        // 解析 UUID
        guard let recordUUID = UUID(uuidString: record.id) else {
            fatalError("无效的 ChatMessage UUID: \(record.id)")
        }
        
        let chatMessage = existing ?? ChatMessageModel(
            id: recordUUID,
            role: "user",
            content: "",
            timestamp: Date()
        )
        
        // 更新基本属性
        chatMessage.id = recordUUID
        chatMessage.role = record.value(for: "role") ?? "user"
        chatMessage.content = record.value(for: "content") ?? ""
        
        // 时间戳处理
        if let timestamp: Date = record.value(for: "timestamp") {
            chatMessage.timestamp = timestamp
        }
        
        // 处理交易ID数据
        if let transactionIdsJsonString: String = record.value(for: "transactionIdsData"),
           let jsonData = transactionIdsJsonString.data(using: .utf8) {
            chatMessage.transactionIdsData = jsonData
        }
        
        // 处理图片数据
        if let assetURL: URL = record.value(for: "imageDataArray") {
            do {
                let data = try Data(contentsOf: assetURL)
                chatMessage.imageDataArray = data
            } catch {
                print("❌ 下载聊天图片数据失败: \(error)")
            }
        }
        
        return chatMessage
    }
    
    /// 检查是否需要同步到 CloudKit
    /// - Returns: 如果需要同步返回 true
    func needsSync() -> Bool {
        // 可以添加逻辑来判断是否需要同步
        // 例如检查内容长度、时间戳等
        return !content.isEmpty
    }
    
    /// 准备同步到 CloudKit
    /// 在同步前调用此方法进行必要的准备
    func prepareForSync() {
        // 聊天消息通常不需要修改时间戳
        // 保持原始的 timestamp
    }
}

// MARK: - 同步辅助方法
extension ChatMessageModel {
    
    /// 同步当前聊天消息到 CloudKit
    func syncToCloud() {
        prepareForSync()
        CloudSyncManager.shared.sync(self)
    }
    
    /// 从 CloudKit 删除当前聊天消息
    func deleteFromCloud() {
        CloudSyncManager.shared.delete(self)
    }
    
    /// 批量同步多个聊天消息
    /// - Parameter messages: 要同步的消息数组
    static func syncMultipleToCloud(_ messages: [ChatMessageModel]) {
        for message in messages {
            message.syncToCloud()
        }
    }
    
    /// 同步最近的聊天消息（例如最近7天）
    /// - Parameters:
    ///   - messages: 所有消息
    ///   - days: 最近多少天
    static func syncRecentMessages(_ messages: [ChatMessageModel], days: Int = 7) {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        let recentMessages = messages.filter { $0.timestamp >= cutoffDate }
        
        print("开始同步最近 \(days) 天的 \(recentMessages.count) 条聊天消息")
        syncMultipleToCloud(recentMessages)
    }
    
    /// 同步用户消息（排除助手消息）
    /// - Parameter messages: 所有消息
    static func syncUserMessages(_ messages: [ChatMessageModel]) {
        let userMessages = messages.filter { $0.role == "user" }
        
        print("开始同步 \(userMessages.count) 条用户消息")
        syncMultipleToCloud(userMessages)
    }
    
    /// 清理和同步聊天历史
    /// 保留最近的消息，删除过旧的消息
    /// - Parameters:
    ///   - messages: 所有消息
    ///   - keepDays: 保留最近多少天的消息
    static func cleanupAndSync(_ messages: [ChatMessageModel], keepDays: Int = 30) {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -keepDays, to: Date()) ?? Date()
        
        // 分离新旧消息
        let recentMessages = messages.filter { $0.timestamp >= cutoffDate }
        let oldMessages = messages.filter { $0.timestamp < cutoffDate }
        
        print("保留最近 \(keepDays) 天的 \(recentMessages.count) 条消息")
        print("将删除 \(oldMessages.count) 条旧消息")
        
        // 同步最近的消息
        syncMultipleToCloud(recentMessages)
        
        // 删除旧消息（可选）
        for oldMessage in oldMessages {
            oldMessage.deleteFromCloud()
        }
    }
}
