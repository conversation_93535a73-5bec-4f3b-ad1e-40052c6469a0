//
//  CloudSyncManager.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import Foundation
import MYCloudKit
import SwiftData

/// SwiftData + MYCloudKit 同步管理器
///
/// 基于 Bad-Habits 项目的 AppState 适配到 SwiftData 架构
@Observable
final class CloudSyncManager {

  // MARK: - 分享类型配置
  enum ShareType {
    case zone  // 整个区域分享
    case recordWithMYZone  // 使用 MYCloudKit 默认区域的记录分享
    case recordWithCustomZone  // 使用自定义区域的记录分享
  }

  // MARK: - 单例
  static let shared = CloudSyncManager()

  // MARK: - 配置
  /// 分享类型配置
  let shareType: ShareType = .recordWithCustomZone

  /// MYCloudKit 同步引擎
  let syncEngine: MYSyncEngine

  /// SwiftData 模型容器
  private var modelContainer: ModelContainer?

  /// 是否已初始化
  private var isInitialized = false

  // MARK: - 初始化
  private init() {
    // 初始化 MYSyncEngine
    self.syncEngine = MYSyncEngine(
      containerIdentifier: "iCloud.cc.nzue.cstory",  // 请替换为您的 CloudKit 容器标识符
      logLevel: .debug
    )

    // 设置委托
    self.syncEngine.delegate = self
  }

  /// 设置 SwiftData 模型容器
  /// - Parameter container: SwiftData 模型容器
  func setModelContainer(_ container: ModelContainer) {
    self.modelContainer = container
    self.isInitialized = true

    // 容器设置完成后，立即开始初始同步 - 按照 Bad-Habits 的方式
    DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
      Task {
        await self.syncEngine.fetch()
      }
    }
  }

  // MARK: - 同步操作

  /// 同步单个记录到 CloudKit
  /// - Parameter record: 实现了 MYRecordConvertible 的记录
  func sync<T: MYRecordConvertible>(_ record: T) {
    guard isInitialized else {
      print("⚠️ CloudSyncManager 未初始化，请先调用 setModelContainer")
      return
    }
    syncEngine.sync(record)
  }

  /// 删除记录
  /// - Parameter record: 要删除的记录
  func delete<T: MYRecordConvertible>(_ record: T) {
    guard isInitialized else {
      print("⚠️ CloudSyncManager 未初始化，请先调用 setModelContainer")
      return
    }
    syncEngine.delete(record)
  }

  /// 手动触发同步
  func triggerSync() {
    guard isInitialized else {
      print("⚠️ CloudSyncManager 未初始化，请先调用 setModelContainer")
      return
    }
    syncEngine.sync()
  }

  /// 手动触发获取
  func triggerFetch() {
    guard isInitialized else {
      print("⚠️ CloudSyncManager 未初始化，请先调用 setModelContainer")
      return
    }
    Task {
      await syncEngine.fetch()
    }
  }

  // MARK: - 批量同步功能

  /// 同步所有本地数据到 CloudKit
  func syncAllLocalData() {
    guard let container = modelContainer else {
      print("❌ ModelContainer 未设置")
      return
    }

    let context = ModelContext(container)

    do {
      // 同步货币
      let currencies = try context.fetch(FetchDescriptor<CurrencyModel>())
      print("开始同步 \(currencies.count) 个货币")
      for currency in currencies {
        currency.syncToCloud()
      }

      // 同步卡片
      let cards = try context.fetch(FetchDescriptor<CardModel>())
      print("开始同步 \(cards.count) 个卡片")
      for card in cards {
        card.syncToCloud()
      }

      // 同步主分类
      let mainCategories = try context.fetch(FetchDescriptor<TransactionMainCategoryModel>())
      print("开始同步 \(mainCategories.count) 个主分类")
      for category in mainCategories {
        category.syncToCloud()
      }

      // 同步子分类
      let subCategories = try context.fetch(FetchDescriptor<TransactionSubCategoryModel>())
      print("开始同步 \(subCategories.count) 个子分类")
      for subCategory in subCategories {
        subCategory.syncToCloud()
      }

      // 同步最近的交易（避免同步过多数据）
      let recentTransactionDescriptor = FetchDescriptor<TransactionModel>(
        sortBy: [SortDescriptor(\.transactionDate, order: .reverse)]
      )
      let allTransactions = try context.fetch(recentTransactionDescriptor)
      TransactionModel.syncRecentTransactions(allTransactions, days: 90)  // 同步最近90天

      // 同步最近的聊天消息
      let recentChatDescriptor = FetchDescriptor<ChatMessageModel>(
        sortBy: [SortDescriptor(\.timestamp, order: .reverse)]
      )
      let allMessages = try context.fetch(recentChatDescriptor)
      ChatMessageModel.syncRecentMessages(allMessages, days: 30)  // 同步最近30天

      print("✅ 所有数据同步请求已发送")

    } catch {
      print("❌ 批量同步失败: \(error)")
    }
  }

  /// 获取本地数据统计
  func getLocalDataStats() -> (
    currencies: Int, cards: Int, mainCategories: Int, subCategories: Int, transactions: Int,
    chatMessages: Int
  ) {
    guard let container = modelContainer else {
      return (0, 0, 0, 0, 0, 0)
    }

    let context = ModelContext(container)

    do {
      let currencyCount = try context.fetchCount(FetchDescriptor<CurrencyModel>())
      let cardCount = try context.fetchCount(FetchDescriptor<CardModel>())
      let mainCategoryCount = try context.fetchCount(
        FetchDescriptor<TransactionMainCategoryModel>())
      let subCategoryCount = try context.fetchCount(FetchDescriptor<TransactionSubCategoryModel>())
      let transactionCount = try context.fetchCount(FetchDescriptor<TransactionModel>())
      let chatMessageCount = try context.fetchCount(FetchDescriptor<ChatMessageModel>())

      return (
        currencyCount, cardCount, mainCategoryCount, subCategoryCount, transactionCount,
        chatMessageCount
      )
    } catch {
      print("❌ 获取数据统计失败: \(error)")
      return (0, 0, 0, 0, 0, 0)
    }
  }
}

// MARK: - MYSyncDelegate 实现
extension CloudSyncManager: MYSyncDelegate {

  /// 处理从 CloudKit 接收到的记录保存
  func didReceiveRecordsToSave(_ records: [MYSyncEngine.FetchedRecord]) {
    guard let container = modelContainer else {
      print("❌ ModelContainer 未设置")
      return
    }

    // 按照 Bad-Habits 的方式，使用主队列处理 - SwiftData 的线程安全处理
    DispatchQueue.main.async {
      let context = ModelContext(container)

      // 处理每个记录
      for record in records {
        switch record.type {
        case "Currency":
          self.handleCurrencyRecord(record, context: context)
        case "Card":
          self.handleCardRecord(record, context: context)
        case "TransactionMainCategory":
          self.handleMainCategoryRecord(record, context: context)
        case "TransactionSubCategory":
          self.handleSubCategoryRecord(record, context: context)
        case "Transaction":
          self.handleTransactionRecord(record, context: context)
        case "ChatMessage":
          self.handleChatMessageRecord(record, context: context)
        default:
          print("⚠️ 未知记录类型: \(record.type)")
        }
      }

      // 保存上下文
      do {
        try context.save()
        print("✅ 成功保存 \(records.count) 条记录")
      } catch {
        print("❌ 保存记录失败: \(error)")
      }
    }
  }

  /// 处理记录删除
  func didReceiveRecordsToDelete(_ records: [(myRecordID: String, myRecordType: MYRecordType)]) {
    guard let container = modelContainer else {
      print("❌ ModelContainer 未设置")
      return
    }

    // 按照 Bad-Habits 的方式，使用主队列处理
    DispatchQueue.main.async {
      let context = ModelContext(container)

      for record in records {
        switch record.myRecordType {
        case "Currency":
          self.deleteCurrencyRecord(recordID: record.myRecordID, context: context)
        case "Card":
          self.deleteCardRecord(recordID: record.myRecordID, context: context)
        case "TransactionMainCategory":
          self.deleteMainCategoryRecord(recordID: record.myRecordID, context: context)
        case "TransactionSubCategory":
          self.deleteSubCategoryRecord(recordID: record.myRecordID, context: context)
        case "Transaction":
          self.deleteTransactionRecord(recordID: record.myRecordID, context: context)
        case "ChatMessage":
          self.deleteChatMessageRecord(recordID: record.myRecordID, context: context)
        default:
          print("⚠️ 未知删除记录类型: \(record.myRecordType)")
        }
      }

      do {
        try context.save()
        print("✅ 成功删除 \(records.count) 条记录")
      } catch {
        print("❌ 删除记录失败: \(error)")
      }
    }
  }

  /// 处理组删除
  func didReceiveGroupIDsToDelete(_ ids: [String]) {
    guard let container = modelContainer else {
      print("❌ ModelContainer 未设置")
      return
    }

    let context = ModelContext(container)

    // 根据组ID删除相关记录
    for groupID in ids {
      // 这里需要根据您的业务逻辑来实现
      // 例如删除某个卡片下的所有交易记录等
      print("🗑️ 删除组: \(groupID)")
    }

    do {
      try context.save()
      print("✅ 成功删除 \(ids.count) 个组")
    } catch {
      print("❌ 删除组失败: \(error)")
    }
  }

  /// 处理无法同步的记录
  func handleUnsyncableRecord(
    recordID: String,
    recordType: MYRecordType,
    reason: String,
    error: any Error
  ) -> [any MYRecordConvertible]? {
    print("⚠️ 无法同步记录 \(recordType):\(recordID) - \(reason)")
    print("错误详情: \(error)")

    // 这里可以实现错误恢复逻辑
    // 返回 nil 表示不重试，返回记录数组表示重试这些记录
    return nil
  }

  /// 定义同步记录类型的依赖顺序
  func syncableRecordTypesInDependencyOrder() -> [MYRecordType] {
    // 按依赖关系排序：基础数据 -> 关联数据
    return [
      "Currency",  // 货币（基础）
      "Card",  // 卡片（依赖货币）
      "TransactionMainCategory",  // 主分类（基础）
      "TransactionSubCategory",  // 子分类（依赖主分类）
      "Transaction",  // 交易（依赖卡片和分类）
      "ChatMessage",  // 聊天消息（相对独立）
    ]
  }
}

// MARK: - 记录处理方法
extension CloudSyncManager {

  // MARK: - Currency 记录处理
  private func handleCurrencyRecord(_ record: MYSyncEngine.FetchedRecord, context: ModelContext) {
    // 查找现有记录
    let predicate = #Predicate<CurrencyModel> { currency in
      currency.code == record.id
    }

    let descriptor = FetchDescriptor<CurrencyModel>(predicate: predicate)
    let existingCurrency = try? context.fetch(descriptor).first

    let currency = existingCurrency ?? CurrencyModel()

    // 更新属性
    currency.code = record.id
    currency.name = record.value(for: "name") ?? ""
    currency.symbol = record.value(for: "symbol") ?? ""
    currency.rate = record.value(for: "rate") ?? 0.0
    currency.defaultRate = record.value(for: "defaultRate") ?? 0.0
    currency.isBaseCurrency = record.value(for: "isBaseCurrency") ?? false
    currency.isCustom = record.value(for: "isCustom") ?? false
    currency.isSelected = record.value(for: "isSelected") ?? true
    currency.order = record.value(for: "order") ?? 999
    currency.updatedAt = Date()

    if existingCurrency == nil {
      context.insert(currency)
    }
  }

  private func deleteCurrencyRecord(recordID: String, context: ModelContext) {
    let predicate = #Predicate<CurrencyModel> { currency in
      currency.code == recordID
    }

    let descriptor = FetchDescriptor<CurrencyModel>(predicate: predicate)
    if let currency = try? context.fetch(descriptor).first {
      context.delete(currency)
    }
  }

  // MARK: - Card 记录处理
  private func handleCardRecord(_ record: MYSyncEngine.FetchedRecord, context: ModelContext) {
    guard let recordUUID = UUID(uuidString: record.id) else {
      print("❌ 无效的 Card UUID: \(record.id)")
      return
    }

    let predicate = #Predicate<CardModel> { card in
      card.id == recordUUID
    }

    let descriptor = FetchDescriptor<CardModel>(predicate: predicate)
    let existingCard = try? context.fetch(descriptor).first

    let card = CardModel.from(record: record, existing: existingCard)

    if existingCard == nil {
      context.insert(card)
    }
  }

  private func deleteCardRecord(recordID: String, context: ModelContext) {
    guard let recordUUID = UUID(uuidString: recordID) else { return }

    let predicate = #Predicate<CardModel> { card in
      card.id == recordUUID
    }

    let descriptor = FetchDescriptor<CardModel>(predicate: predicate)
    if let card = try? context.fetch(descriptor).first {
      context.delete(card)
    }
  }

  // MARK: - 分类记录处理
  private func handleMainCategoryRecord(_ record: MYSyncEngine.FetchedRecord, context: ModelContext)
  {
    let predicate = #Predicate<TransactionMainCategoryModel> { category in
      category.id == record.id
    }

    let descriptor = FetchDescriptor<TransactionMainCategoryModel>(predicate: predicate)
    let existingCategory = try? context.fetch(descriptor).first

    let category = TransactionMainCategoryModel.from(record: record, existing: existingCategory)

    if existingCategory == nil {
      context.insert(category)
    }
  }

  private func deleteMainCategoryRecord(recordID: String, context: ModelContext) {
    let predicate = #Predicate<TransactionMainCategoryModel> { category in
      category.id == recordID
    }

    let descriptor = FetchDescriptor<TransactionMainCategoryModel>(predicate: predicate)
    if let category = try? context.fetch(descriptor).first {
      context.delete(category)
    }
  }

  private func handleSubCategoryRecord(_ record: MYSyncEngine.FetchedRecord, context: ModelContext)
  {
    let predicate = #Predicate<TransactionSubCategoryModel> { subCategory in
      subCategory.id == record.id
    }

    let descriptor = FetchDescriptor<TransactionSubCategoryModel>(predicate: predicate)
    let existingSubCategory = try? context.fetch(descriptor).first

    let subCategory = TransactionSubCategoryModel.from(
      record: record, existing: existingSubCategory)

    if existingSubCategory == nil {
      context.insert(subCategory)
    }
  }

  private func deleteSubCategoryRecord(recordID: String, context: ModelContext) {
    let predicate = #Predicate<TransactionSubCategoryModel> { subCategory in
      subCategory.id == recordID
    }

    let descriptor = FetchDescriptor<TransactionSubCategoryModel>(predicate: predicate)
    if let subCategory = try? context.fetch(descriptor).first {
      context.delete(subCategory)
    }
  }

  // MARK: - 交易记录处理
  private func handleTransactionRecord(_ record: MYSyncEngine.FetchedRecord, context: ModelContext)
  {
    guard let recordUUID = UUID(uuidString: record.id) else {
      print("❌ 无效的 Transaction UUID: \(record.id)")
      return
    }

    let predicate = #Predicate<TransactionModel> { transaction in
      transaction.id == recordUUID
    }

    let descriptor = FetchDescriptor<TransactionModel>(predicate: predicate)
    let existingTransaction = try? context.fetch(descriptor).first

    let transaction = TransactionModel.from(record: record, existing: existingTransaction)

    if existingTransaction == nil {
      context.insert(transaction)
    }
  }

  private func deleteTransactionRecord(recordID: String, context: ModelContext) {
    guard let recordUUID = UUID(uuidString: recordID) else { return }

    let predicate = #Predicate<TransactionModel> { transaction in
      transaction.id == recordUUID
    }

    let descriptor = FetchDescriptor<TransactionModel>(predicate: predicate)
    if let transaction = try? context.fetch(descriptor).first {
      context.delete(transaction)
    }
  }

  // MARK: - 聊天消息处理
  private func handleChatMessageRecord(_ record: MYSyncEngine.FetchedRecord, context: ModelContext)
  {
    guard let recordUUID = UUID(uuidString: record.id) else {
      print("❌ 无效的 ChatMessage UUID: \(record.id)")
      return
    }

    let predicate = #Predicate<ChatMessageModel> { message in
      message.id == recordUUID
    }

    let descriptor = FetchDescriptor<ChatMessageModel>(predicate: predicate)
    let existingMessage = try? context.fetch(descriptor).first

    let message = ChatMessageModel.from(record: record, existing: existingMessage)

    if existingMessage == nil {
      context.insert(message)
    }
  }

  private func deleteChatMessageRecord(recordID: String, context: ModelContext) {
    guard let recordUUID = UUID(uuidString: recordID) else { return }

    let predicate = #Predicate<ChatMessageModel> { message in
      message.id == recordUUID
    }

    let descriptor = FetchDescriptor<ChatMessageModel>(predicate: predicate)
    if let message = try? context.fetch(descriptor).first {
      context.delete(message)
    }
  }
}
