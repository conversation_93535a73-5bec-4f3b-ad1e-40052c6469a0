//
//  HomeView.swift
//  CStory
//
//  Created by 咩咩 on 2025/7/18.
//

import MYCloudKit
import SwiftUI

/// 主页视图
///
/// 这是一个纯粹的UI组件，它接收一个`HomeVM`对象并将其数据显示出来。
/// 所有的业务逻辑、数据格式化均由VM处理。
/// 使用新架构的MVVM模式，提供现代化的主页体验。
struct HomeView: View {

  // MARK: - 属性

  // 环境变量
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper
  @State private var showingCardDetail = false
  @State private var selectedCard: HomeCardDisplayData?

  // CloudKit 同步相关
  @State private var syncManager = CloudSyncManager.shared
  @State private var showingSyncDetails = false

  // 底部按钮相关状态
  @State private var showCreateTransactionView = false
  @State private var showInsightSheet = false
  @State private var showSettingView = false
  @State private var showCircleExplanationSheet = false

  // View只依赖于VM，不再关心原始数据
  private var viewModel: HomeVM {
    HomeVM(
      dataManager: dataManager,
      onTransactionTap: { transaction in
        // 震动反馈
        dataManager.hapticManager.trigger(.selection)
        // 导航到交易详情页面
        pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
      }
    )
  }

  // MARK: - 主体视图

  var body: some View {
    ZStack {
      // MARK: 主要内容区域
      ScrollView(.vertical, showsIndicators: false) {
        VStack(spacing: 0) {
          // MARK: 净资产区域
          VStack(spacing: 0) {
            HStack {
              TitleKit(
                viewModel: TitleKitVM.titleOnly(title: "净资产")
              )
              .opacity(0.6)

              Spacer()

              // CloudKit 同步状态指示器
              cloudSyncStatusView
            }
            .padding(.horizontal, 16)

            HStack {
              DisplayCurrencyView.size32(
                symbol: viewModel.netAssetSymbol,
                amount: viewModel.netAssetAmount)
              Spacer()
              ActionButton(
                viewModel: ActionButtonVM.viewAllButton {
                  pathManager.path.append(NavigationDestination.cardBagView)
                }
              )
            }
            .padding(.bottom, 12)
            .padding(.horizontal, 16)
          }

          // MARK: 卡片横向滚动区域
          ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
              // 显示所有选中的卡片
              ForEach(Array(viewModel.homeCardDisplayData.enumerated()), id: \.offset) {
                index, cardData in
                cardView(
                  balance: cardData.balance,
                  symbol: cardData.symbol,
                  isCredit: cardData.isCredit,
                  coverImageName: cardData.coverImageName,
                  textColor: cardData.textColor,
                  bankLogo: cardData.bankLogo
                )
                .onTapGesture {
                  dataManager.hapticManager.trigger(.selection)
                  selectedCard = cardData
                  withAnimation(.easeInOut(duration: 0.3)) {
                    showingCardDetail = true
                  }
                }
              }

              // 创建卡片按钮
              AddCardButton(
                viewModel: AddCardButtonVM(
                  style: .minimal,
                  action: {
                    pathManager.path.append(NavigationDestination.cardCategoryView)
                  },
                  shouldDismiss: false  // 主页不在 sheet 中，不需要关闭
                )
              )
            }
            .padding(.horizontal, 16)
          }

          // MARK: 统计区域
          VStack(spacing: 0) {
            TitleKit(
              viewModel: TitleKitVM.titleOnly(title: "统计")
            )
            HStack(alignment: .center, spacing: 12) {
              // 左侧多圆环进度显示区域（正方形）
              VStack {
                CircularProgress(
                  savingsRatio: viewModel.circularProgressData.savingsRatio,
                  creditRatio: viewModel.circularProgressData.creditRatio,
                  cashFlowScore: viewModel.circularProgressData.cashFlowScore,
                  size: 90
                )
              }
              .frame(width: 148, height: 148)
              .background(.cWhite)
              .cornerRadius(16)
              .overlay(
                RoundedRectangle(cornerRadius: 16)
                  .strokeBorder(Color.accentColor.opacity(0.08), lineWidth: 1)
              )
              .onTapGesture {
                dataManager.hapticManager.trigger(.selection)
                showCircleExplanationSheet = true
              }

              // 右侧统计卡片区域 - 精确对齐高度
              VStack(spacing: 12) {
                // 总资产卡片 - 让内容填满高度
                let totalAsset = viewModel.totalAssetData
                HomeStatisticCard(
                  title: totalAsset.title,
                  amount: totalAsset.amount,
                  currencySymbol: totalAsset.currencySymbol,
                  iconName: totalAsset.iconName
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)

                // 总负债卡片 - 让内容填满高度
                let totalLiability = viewModel.totalLiabilityData
                HomeStatisticCard(
                  title: totalLiability.title,
                  amount: totalLiability.amount,
                  currencySymbol: totalLiability.currencySymbol,
                  iconName: totalLiability.iconName
                )
                .frame(maxWidth: .infinity, maxHeight: .infinity)
              }
              .frame(maxWidth: .infinity, maxHeight: 148)
            }
            .padding(.horizontal, 16)
          }

          // MARK: 最近交易区域
          TitleKit(
            viewModel: TitleKitVM(
              title: "最近交易",
              rightButtonTitle: "全部交易",
              rightButtonAction: {
                pathManager.path.append(NavigationDestination.transactionRecordView)
              }
            )
          )

          // 最近交易列表 - 使用统一的TransactionListContent组件
          VStack(spacing: 0) {
            TransactionListContent(
              transactionDayGroups: viewModel.recentTransactionDayGroups,
              currencySymbol: viewModel.netAssetSymbol
            )
            .padding(.bottom, 100)  // 为浮动按钮留出空间
          }
        }
      }
      .background(.cLightBlue)
      .edgesIgnoringSafeArea(.bottom)

      // MARK: 浮动操作按钮
      FloatingActionButtonView(
        buttons: [
          FloatingActionButton(
            iconName: "setting_icon",
            action: {
              dataManager.hapticManager.trigger(.impactLight)
              showSettingView = true
            }),
          FloatingActionButton(
            title: "记一笔",
            action: {
              dataManager.hapticManager.trigger(.impactMedium)
              showCreateTransactionView = true
            }, style: .primary),
          FloatingActionButton(
            iconName: "insight_icon",
            action: {
              dataManager.hapticManager.trigger(.impactLight)
              showInsightSheet = true
            }),
        ]
      )
    }
    .background(.cLightBlue)
    //    .background(Color.accentColor)
    // 圆环数据解释Sheet
    .floatingSheet(
      isPresented: $showCircleExplanationSheet,
      config: SheetBase(
        maxDetent: .height(400),
        cornerRadius: 24,
        interactiveDimiss: false,
        hPadding: 8,
        bPadding: 4
      )
    ) {
      CircleExplanationSheet(
        savingsRatio: viewModel.circularProgressData.savingsRatio,
        creditAvailableRatio: viewModel.circularProgressData.creditRatio,
        cashFlowHealthScore: viewModel.circularProgressData.cashFlowScore,
        dismiss: {
          showCircleExplanationSheet = false
        }
      )
    }
    // CloudKit 同步详情 Sheet
    .sheet(isPresented: $showingSyncDetails) {
      CloudSyncDetailView()
    }
    // 注释掉重复的同步调用，因为 CloudSyncManager 已经在 setModelContainer 时自动触发
    // .task {
    //   // 延迟一秒后触发获取，避免启动时的性能影响
    //   try? await Task.sleep(nanoseconds: 1_000_000_000)
    //   syncManager.triggerFetch()
    // }

    // 卡片详情overlay
    .overlay {
      if showingCardDetail, let selectedCard = selectedCard {
        // 根据selectedCard的id找到对应的真实CardModel
        let realCard = dataManager.cards.first { $0.id == selectedCard.id }

        CardDetailView(
          card: realCard,  // 传递真实的CardModel，与正式首页保持一致
          cardNamespace: nil,
          showingCardDetail: $showingCardDetail,
          pathManager: pathManager,
          transactions: dataManager.allTransactions,
          currencies: dataManager.currencies,
          animationMode: .slideFromTop,
          isCreatingCard: false,
          isCredit: nil,  // 让CardDetailView从CardModel中获取，与正式首页保持一致
          mainCategory: nil,
          subCategory: nil,
          onCardCreated: nil,
          dataManager: dataManager
        )
      }
    }

    // MARK: - Sheet展示

    // 记账页面overlay
    .overlay {
      if showCreateTransactionView {
        CreateTransactionView(
          showCreateTransactionView: $showCreateTransactionView
        )
      }
    }

    // 设置Sheet
    .floatingSheet(
      isPresented: $showSettingView,
      config: SheetBase(
        maxDetent: .fraction(0.99), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      SettingViewWrapper()
    }

    // 洞察Sheet
    .floatingSheet(
      isPresented: $showInsightSheet,
      config: SheetBase(
        maxDetent: .fraction(0.99), cornerRadius: 24, interactiveDimiss: false, hPadding: 8,
        bPadding: 4)
    ) {
      InsightSheet(dataManager: dataManager)
    }
  }

  // MARK: - 子视图构建器

  /// 卡片视图
  private func cardView(
    balance: Double,
    symbol: String,
    isCredit: Bool,
    coverImageName: String,
    textColor: Color,
    bankLogo: Data?
  ) -> some View {
    VStack(spacing: 0) {
      HStack {
        Spacer()
        // 卡片图标 - 使用银行Logo或默认图标
        cardIcon(bankLogo: bankLogo)
      }
      Spacer()
      HStack {
        DisplayCurrencyView.size12(
          symbol: symbol,
          amount: balance
        )
        .color(textColor)
        Spacer()
      }
    }
    .padding(6)
    .background(
      Image(coverImageName)
        .resizable()
        .aspectRatio(contentMode: .fill)
    )
    .frame(width: 100, height: 60)
    .cornerRadius(16)
  }

  /// 卡片图标
  private func cardIcon(bankLogo: Data?) -> some View {
    IconView(
      viewModel: IconViewVM.optionalImage(
        bankLogo,
        size: 16,
        style: IconStyle(
          backgroundColor: .cWhite,
          cornerRadius: 4
        )
      )
    )
  }

  // MARK: - CloudKit 同步状态视图

  /// CloudKit 同步状态指示器
  private var cloudSyncStatusView: some View {
    Button(action: {
      showingSyncDetails = true
    }) {
      HStack(spacing: 4) {
        // 同步状态图标
        Image(systemName: syncStatusIcon)
          .font(.system(size: 12, weight: .medium))
          .foregroundColor(syncStatusColor)

        // 同步状态文字（可选显示）
        if case .syncing = syncManager.syncEngine.syncState {
          Text("同步中")
            .font(.system(size: 10, weight: .medium))
            .foregroundColor(syncStatusColor)
        }
      }
      .padding(.horizontal, 8)
      .padding(.vertical, 4)
      .background(syncStatusColor.opacity(0.1))
      .cornerRadius(8)
    }
    .buttonStyle(PlainButtonStyle())
  }

  /// 同步状态图标
  private var syncStatusIcon: String {
    switch syncManager.syncEngine.syncState {
    case .idle:
      return "icloud"
    case .syncing:
      return "icloud.and.arrow.up"
    case .stopped:
      return "icloud.slash"
    case .completed:
      return "icloud"
    }
  }

  /// 同步状态颜色
  private var syncStatusColor: Color {
    switch syncManager.syncEngine.syncState {
    case .idle:
      return .green
    case .syncing:
      return .blue
    case .stopped:
      return .red
    case .completed:
      return .green
    }
  }

}

// MARK: - CloudKit 同步详情视图
struct CloudSyncDetailView: View {
  @Environment(\.dismiss) private var dismiss
  @State private var syncManager = CloudSyncManager.shared

  var body: some View {
    NavigationView {
      List {
        // 同步状态部分
        Section("同步状态") {
          syncStatusSection
        }

        // 快速操作部分
        Section("快速操作") {
          quickActionsSection
        }

        // 数据统计部分
        Section("数据统计") {
          dataStatsSection
        }
      }
      .navigationTitle("CloudKit 同步")
      .navigationBarTitleDisplayMode(.inline)
      .toolbar {
        ToolbarItem(placement: .navigationBarTrailing) {
          Button("完成") {
            dismiss()
          }
        }
      }
    }
  }

  // MARK: - 同步状态部分
  private var syncStatusSection: some View {
    VStack(alignment: .leading, spacing: 8) {
      HStack {
        Image(systemName: "icloud")
        Text("同步引擎状态")
        Spacer()
        Circle()
          .fill(getSyncStatusIndicatorColor())
          .frame(width: 8, height: 8)
      }

      Text("同步状态: \(syncStateDescription)")
        .font(.caption)
        .foregroundColor(.secondary)

      Text("获取状态: \(fetchStateDescription)")
        .font(.caption)
        .foregroundColor(.secondary)
    }
  }

  // MARK: - 快速操作部分
  private var quickActionsSection: some View {
    VStack(spacing: 8) {
      Button("手动触发同步") {
        syncManager.triggerSync()
      }
      .buttonStyle(.borderedProminent)
      .frame(maxWidth: .infinity)

      Button("手动触发获取") {
        syncManager.triggerFetch()
      }
      .buttonStyle(.bordered)
      .frame(maxWidth: .infinity)

      Button("同步所有数据") {
        syncManager.syncAllLocalData()
      }
      .buttonStyle(.bordered)
      .frame(maxWidth: .infinity)
    }
  }

  // MARK: - 数据统计部分
  private var dataStatsSection: some View {
    VStack(alignment: .leading, spacing: 4) {
      Text("本地数据概览")
        .font(.headline)

      let stats = syncManager.getLocalDataStats()
      Text("• 货币: \(stats.currencies)")
        .font(.caption)
      Text("• 卡片: \(stats.cards)")
        .font(.caption)
      Text("• 主分类: \(stats.mainCategories)")
        .font(.caption)
      Text("• 子分类: \(stats.subCategories)")
        .font(.caption)
      Text("• 交易: \(stats.transactions)")
        .font(.caption)
      Text("• 聊天消息: \(stats.chatMessages)")
        .font(.caption)
    }
  }

  // MARK: - 辅助方法
  private var syncStateDescription: String {
    switch syncManager.syncEngine.syncState {
    case .idle:
      return "空闲"
    case .syncing(let queueCount):
      return "同步中 (队列: \(queueCount))"
    case .stopped(let queueCount, let error):
      if let error = error {
        return "已停止: \(error.localizedDescription) (队列: \(queueCount))"
      } else {
        return "已停止 (队列: \(queueCount))"
      }
    case .completed(let date):
      let formatter = DateFormatter()
      formatter.timeStyle = .short
      return "已完成 (\(formatter.string(from: date)))"
    }
  }

  private var fetchStateDescription: String {
    switch syncManager.syncEngine.fetchState {
    case .idle:
      return "空闲"
    case .fetching:
      return "获取中"
    case .stopped(let error):
      return "已停止: \(error.localizedDescription)"
    case .completed(let date):
      let formatter = DateFormatter()
      formatter.timeStyle = .short
      return "已完成 (\(formatter.string(from: date)))"
    }
  }

  private func getSyncStatusIndicatorColor() -> Color {
    switch syncManager.syncEngine.syncState {
    case .idle:
      return .green
    case .syncing:
      return .blue
    case .stopped:
      return .red
    case .completed:
      return .green
    }
  }

}

// MARK: - 预览代码 (Preview Provider)

#if DEBUG
  struct HomeView_Previews: PreviewProvider {
    static var previews: some View {
      HomeView()
        .withDataManager(createPreviewDataManager())
    }

    /// 创建预览用的DataManagement
    static func createPreviewDataManager() -> DataManagement {
      // 创建示例货币
      let currencies = [
        CurrencyModel(
          name: "人民币",
          code: "CNY",
          symbol: "¥",
          rate: 1.0,
          isBaseCurrency: true,
          order: 0
        )
      ]

      // 创建示例卡片
      let cards = [
        CardModel(
          id: UUID(),
          order: 0,
          isCredit: false,
          isSelected: true,
          name: "招商银行储蓄卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: 12580.50,
          credit: 0,
          isStatistics: true,
          cover: "card1",
          bankName: "招商银行",
          cardNumber: "1234",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
        CardModel(
          id: UUID(),
          order: 1,
          isCredit: true,
          isSelected: true,
          name: "工商银行信用卡",
          remark: "",
          currency: "CNY",
          symbol: "¥",
          balance: -2580.30,
          credit: 10000.0,
          isStatistics: true,
          cover: "card2",
          bankName: "工商银行",
          cardNumber: "5678",
          isFixedDueDay: true,
          createdAt: Date(),
          updatedAt: Date()
        ),
      ]

      // 创建示例主分类
      let mainCategories = [
        TransactionMainCategoryModel(
          id: "expense_shopping",
          name: "购物",
          icon: .emoji("🛒"),
          order: 0,
          type: "expense"
        ),
        TransactionMainCategoryModel(
          id: "income_salary",
          name: "收入",
          icon: .emoji("💰"),
          order: 0,
          type: "income"
        ),
      ]

      // 创建示例子分类
      let subCategories = [
        TransactionSubCategoryModel(
          id: "shopping_daily",
          name: "日常用品",
          icon: .emoji("🧴"),
          order: 0,
          mainId: "expense_shopping"
        ),
        TransactionSubCategoryModel(
          id: "salary_main",
          name: "工资",
          icon: .emoji("💼"),
          order: 0,
          mainId: "income_salary"
        ),
      ]

      // 创建示例交易
      let now = Date()
      let yesterday = Calendar.current.date(byAdding: .day, value: -1, to: now) ?? now
      let recentTransactions = [
        TransactionModel(
          id: UUID(),
          transactionType: .expense,
          transactionCategoryId: "shopping_daily",
          fromCardId: cards[0].id,
          transactionAmount: 128.50,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "",
          transactionDate: now,
          createdAt: now,
          updatedAt: now
        ),
        TransactionModel(
          id: UUID(),
          transactionType: .income,
          transactionCategoryId: "salary_main",
          toCardId: cards[1].id,
          transactionAmount: 8500.00,
          currency: "CNY",
          symbol: "¥",
          expenseToCardRate: 1.0,
          expenseToBaseRate: 1.0,
          incomeToCardRate: 1.0,
          incomeToBaseRate: 1.0,
          isStatistics: true,
          remark: "",
          transactionDate: yesterday,
          createdAt: yesterday,
          updatedAt: yesterday
        ),
      ]

      return DataManagement(
        cards: cards,
        mainCategories: mainCategories,
        subCategories: subCategories,
        currencies: currencies,
        recentTransactions: recentTransactions,
        allTransactions: recentTransactions,
        chatMessages: []
      )
    }
  }
#endif
