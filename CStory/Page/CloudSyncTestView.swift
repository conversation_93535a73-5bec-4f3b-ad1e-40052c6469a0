//
//  CloudSyncTestView.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import SwiftUI
import SwiftData
import MYCloudKit

/// CloudKit 同步功能测试视图
struct CloudSyncTestView: View {
    @Environment(\.modelContext) private var modelContext
    @Query private var currencies: [CurrencyModel]
    @Query private var cards: [CardModel]
    
    @State private var syncManager = CloudSyncManager.shared
    @State private var showingAddCurrency = false
    @State private var showingAddCard = false
    
    var body: some View {
        NavigationView {
            List {
                // 同步状态部分
                Section("同步状态") {
                    syncStatusView
                }
                
                // 货币管理部分
                Section("货币管理") {
                    currencySection
                }
                
                // 卡片管理部分
                Section("卡片管理") {
                    cardSection
                }
                
                // 同步操作部分
                Section("同步操作") {
                    syncActionsView
                }
            }
            .navigationTitle("CloudKit 同步测试")
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Menu("添加") {
                        Button("添加货币") {
                            showingAddCurrency = true
                        }
                        Button("添加卡片") {
                            showingAddCard = true
                        }
                    }
                }
            }
        }
        .sheet(isPresented: $showingAddCurrency) {
            AddCurrencyView()
        }
        .sheet(isPresented: $showingAddCard) {
            AddCardView()
        }
    }
    
    // MARK: - 同步状态视图
    private var syncStatusView: some View {
        VStack(alignment: .leading, spacing: 8) {
            HStack {
                Image(systemName: "icloud")
                Text("同步引擎状态")
                Spacer()
                Circle()
                    .fill(syncManager.syncEngine.syncState == .idle ? .green : .orange)
                    .frame(width: 8, height: 8)
            }
            
            Text("同步状态: \(syncStateDescription)")
                .font(.caption)
                .foregroundColor(.secondary)
            
            Text("获取状态: \(fetchStateDescription)")
                .font(.caption)
                .foregroundColor(.secondary)
        }
    }
    
    private var syncStateDescription: String {
        switch syncManager.syncEngine.syncState {
        case .idle:
            return "空闲"
        case .syncing:
            return "同步中"
        case .error(let error):
            return "错误: \(error.localizedDescription)"
        }
    }
    
    private var fetchStateDescription: String {
        switch syncManager.syncEngine.fetchState {
        case .idle:
            return "空闲"
        case .fetching:
            return "获取中"
        case .error(let error):
            return "错误: \(error.localizedDescription)"
        }
    }
    
    // MARK: - 货币部分
    private var currencySection: some View {
        Group {
            ForEach(currencies, id: \.code) { currency in
                HStack {
                    VStack(alignment: .leading) {
                        Text(currency.name)
                            .font(.headline)
                        Text("\(currency.code) - \(currency.symbol)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("同步") {
                        currency.syncToCloud()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
            .onDelete(perform: deleteCurrencies)
        }
    }
    
    // MARK: - 卡片部分
    private var cardSection: some View {
        Group {
            ForEach(cards, id: \.id) { card in
                HStack {
                    VStack(alignment: .leading) {
                        Text(card.name)
                            .font(.headline)
                        Text("\(card.currency) - \(card.symbol)\(String(format: "%.2f", card.balance))")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    Spacer()
                    
                    Button("同步") {
                        card.syncToCloud()
                    }
                    .buttonStyle(.bordered)
                    .controlSize(.small)
                }
            }
            .onDelete(perform: deleteCards)
        }
    }
    
    // MARK: - 同步操作视图
    private var syncActionsView: some View {
        VStack(spacing: 12) {
            Button("手动触发同步") {
                syncManager.triggerSync()
            }
            .buttonStyle(.borderedProminent)
            
            Button("手动触发获取") {
                syncManager.triggerFetch()
            }
            .buttonStyle(.bordered)
            
            Button("同步所有货币") {
                for currency in currencies {
                    currency.syncToCloud()
                }
            }
            .buttonStyle(.bordered)
            
            Button("同步所有卡片") {
                CardModel.syncMultipleToCloud(cards)
            }
            .buttonStyle(.bordered)
        }
    }
    
    // MARK: - 删除操作
    private func deleteCurrencies(offsets: IndexSet) {
        withAnimation {
            for index in offsets {
                let currency = currencies[index]
                currency.deleteFromCloud() // 从 CloudKit 删除
                modelContext.delete(currency) // 从本地删除
            }
        }
    }
    
    private func deleteCards(offsets: IndexSet) {
        withAnimation {
            for index in offsets {
                let card = cards[index]
                card.deleteFromCloud() // 从 CloudKit 删除
                modelContext.delete(card) // 从本地删除
            }
        }
    }
}

// MARK: - 添加货币视图
struct AddCurrencyView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    
    @State private var name = ""
    @State private var code = ""
    @State private var symbol = ""
    @State private var rate = 1.0
    
    var body: some View {
        NavigationView {
            Form {
                Section("货币信息") {
                    TextField("货币名称", text: $name)
                    TextField("货币代码", text: $code)
                    TextField("货币符号", text: $symbol)
                    TextField("汇率", value: $rate, format: .number)
                }
            }
            .navigationTitle("添加货币")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveCurrency()
                    }
                    .disabled(name.isEmpty || code.isEmpty)
                }
            }
        }
    }
    
    private func saveCurrency() {
        let currency = CurrencyModel(
            name: name,
            code: code,
            symbol: symbol,
            rate: rate,
            defaultRate: rate
        )
        
        modelContext.insert(currency)
        
        do {
            try modelContext.save()
            // 自动同步到 CloudKit
            currency.syncToCloud()
            dismiss()
        } catch {
            print("保存货币失败: \(error)")
        }
    }
}

// MARK: - 添加卡片视图
struct AddCardView: View {
    @Environment(\.dismiss) private var dismiss
    @Environment(\.modelContext) private var modelContext
    
    @State private var name = ""
    @State private var currency = "USD"
    @State private var symbol = "$"
    @State private var balance = 0.0
    @State private var isCredit = false
    
    var body: some View {
        NavigationView {
            Form {
                Section("卡片信息") {
                    TextField("卡片名称", text: $name)
                    TextField("货币代码", text: $currency)
                    TextField("货币符号", text: $symbol)
                    TextField("余额", value: $balance, format: .number)
                    Toggle("信用卡", isOn: $isCredit)
                }
            }
            .navigationTitle("添加卡片")
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button("取消") {
                        dismiss()
                    }
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button("保存") {
                        saveCard()
                    }
                    .disabled(name.isEmpty)
                }
            }
        }
    }
    
    private func saveCard() {
        let card = CardModel()
        card.name = name
        card.currency = currency
        card.symbol = symbol
        card.balance = balance
        card.isCredit = isCredit
        
        modelContext.insert(card)
        
        do {
            try modelContext.save()
            // 自动同步到 CloudKit
            card.syncToCloud()
            dismiss()
        } catch {
            print("保存卡片失败: \(error)")
        }
    }
}

#Preview {
    CloudSyncTestView()
        .modelContainer(for: [CurrencyModel.self, CardModel.self], inMemory: true)
}
