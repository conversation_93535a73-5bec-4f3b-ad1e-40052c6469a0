//
//  CurrencyModel+MYRecordConvertible.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import CloudKit
import Foundation
import MYCloudKit

/// CurrencyModel 的 MYRecordConvertible 实现
///
/// 将 SwiftData 的 CurrencyModel 适配到 MYCloudKit 同步系统
extension CurrencyModel: MYRecordConvertible {

  /// CloudKit 记录的唯一标识符
  /// 使用货币代码作为唯一标识符（如 "USD", "EUR"）
  public var myRecordID: String {
    return code.isEmpty ? UUID().uuidString : code
  }

  /// CloudKit 记录类型
  public var myRecordType: String {
    return "Currency"
  }

  /// 根组ID - 按照 Bad-Habits 的方式实现
  public var myRootGroupID: String? {
    switch CloudSyncManager.shared.shareType {
    case .recordWithMYZone:
      return nil
    case .zone, .recordWithCustomZone:
      return code  // 使用货币代码作为组ID，每个货币是独立的组
    }
  }

  /// 父记录ID - 货币没有父记录
  public var myParentID: String? {
    return nil
  }

  /// 需要同步到 CloudKit 的属性
  public var myProperties: [String: MYRecordValue] {
    return [
      "name": .string(name),
      "code": .string(code),
      "symbol": .string(symbol),
      "rate": .double(rate),
      "defaultRate": .double(defaultRate),
      "isBaseCurrency": .bool(isBaseCurrency),
      "isCustom": .bool(isCustom),
      "isSelected": .bool(isSelected),
      "order": .int64(Int64(order)),
      "createdAt": .date(createdAt),
      "updatedAt": .date(updatedAt),
    ]
  }
}

// MARK: - 便利方法
extension CurrencyModel {

  /// 从 CloudKit 记录创建或更新 CurrencyModel
  /// - Parameters:
  ///   - record: CloudKit 获取的记录
  ///   - existing: 现有的 CurrencyModel 实例（可选）
  /// - Returns: 更新后的 CurrencyModel
  static func from(record: MYSyncEngine.FetchedRecord, existing: CurrencyModel? = nil)
    -> CurrencyModel
  {
    let currency = existing ?? CurrencyModel()

    // 更新属性
    currency.name = record.value(for: "name") ?? ""
    currency.code = record.value(for: "code") ?? record.id
    currency.symbol = record.value(for: "symbol") ?? ""
    currency.rate = record.value(for: "rate") ?? 0.0
    currency.defaultRate = record.value(for: "defaultRate") ?? 0.0
    currency.isBaseCurrency = record.value(for: "isBaseCurrency") ?? false
    currency.isCustom = record.value(for: "isCustom") ?? false
    currency.isSelected = record.value(for: "isSelected") ?? true
    currency.order = Int(record.value(for: "order") as? Int64 ?? 999)

    // 时间戳处理
    if let createdAt: Date = record.value(for: "createdAt") {
      currency.createdAt = createdAt
    }
    if let updatedAt: Date = record.value(for: "updatedAt") {
      currency.updatedAt = updatedAt
    } else {
      currency.updatedAt = Date()
    }

    return currency
  }

  /// 检查是否需要同步到 CloudKit
  /// - Returns: 如果需要同步返回 true
  func needsSync() -> Bool {
    // 这里可以添加逻辑来判断是否需要同步
    // 例如检查 updatedAt 时间戳等
    return true
  }

  /// 准备同步到 CloudKit
  /// 在同步前调用此方法来更新时间戳等
  func prepareForSync() {
    self.updatedAt = Date()
  }
}

// MARK: - 同步辅助方法
extension CurrencyModel {

  /// 同步当前货币到 CloudKit
  func syncToCloud() {
    prepareForSync()
    CloudSyncManager.shared.sync(self)
  }

  /// 从 CloudKit 删除当前货币
  func deleteFromCloud() {
    CloudSyncManager.shared.delete(self)
  }
}
