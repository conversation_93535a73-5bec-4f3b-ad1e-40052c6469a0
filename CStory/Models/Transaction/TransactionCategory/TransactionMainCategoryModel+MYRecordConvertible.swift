//
//  TransactionMainCategoryModel+MYRecordConvertible.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import Foundation
import MYCloudKit
import CloudKit

/// TransactionMainCategoryModel 的 MYRecordConvertible 实现
extension TransactionMainCategoryModel: MYRecordConvertible {
    
    /// CloudKit 记录的唯一标识符
    public var myRecordID: String {
        return id
    }
    
    /// CloudKit 记录类型
    public var myRecordType: String {
        return "TransactionMainCategory"
    }
    
    /// 根组ID - 用于分组相关记录
    public var myRootGroupID: String? {
        switch CloudSyncManager.shared.shareType {
        case .recordWithMYZone:
            return nil
        case .zone, .recordWithCustomZone:
            return "CategoryGroup" // 所有分类在同一个组中
        }
    }
    
    /// 父记录ID - 主分类没有父记录
    public var myParentID: String? {
        return nil
    }
    
    /// 需要同步到 CloudKit 的属性
    public var myProperties: [String: MYRecordValue] {
        var properties: [String: MYRecordValue] = [
            "name": .string(name),
            "order": .int64(Int64(order)),
            "type": .string(type)
        ]
        
        // 图标处理 - 根据 IconType 类型处理
        switch icon {
        case .emoji(let emojiString):
            properties["iconType"] = .string("emoji")
            properties["iconValue"] = .string(emojiString)
        case .image(let imageData):
            // 创建临时文件用于上传图片
            if let tempURL = createTempFileForIcon(imageData) {
                properties["iconType"] = .string("image")
                properties["iconValue"] = .asset(tempURL)
            }
        case .systemImage(let systemName):
            properties["iconType"] = .string("systemImage")
            properties["iconValue"] = .string(systemName)
        }
        
        return properties
    }
    
    /// 为图标创建临时文件
    /// - Parameter data: 图片数据
    /// - Returns: 临时文件URL
    private func createTempFileForIcon(_ data: Data) -> URL? {
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = "categoryIcon_\(id).png"
        let tempURL = tempDir.appendingPathComponent(fileName)
        
        do {
            try data.write(to: tempURL)
            return tempURL
        } catch {
            print("❌ 创建分类图标临时文件失败: \(error)")
            return nil
        }
    }
}

// MARK: - 便利方法
extension TransactionMainCategoryModel {
    
    /// 从 CloudKit 记录创建或更新 TransactionMainCategoryModel
    /// - Parameters:
    ///   - record: CloudKit 获取的记录
    ///   - existing: 现有的实例（可选）
    /// - Returns: 更新后的实例
    static func from(record: MYSyncEngine.FetchedRecord, existing: TransactionMainCategoryModel? = nil) -> TransactionMainCategoryModel {
        let category = existing ?? TransactionMainCategoryModel(
            id: record.id,
            name: "",
            icon: .emoji("📁"),
            order: 0,
            type: TransactionType.expense.rawValue
        )
        
        // 更新基本属性
        category.id = record.id
        category.name = record.value(for: "name") ?? ""
        category.order = Int(record.value(for: "order") as? Int64 ?? 0)
        category.type = record.value(for: "type") ?? TransactionType.expense.rawValue
        
        // 图标处理
        if let iconType: String = record.value(for: "iconType") {
            switch iconType {
            case "emoji":
                if let emojiValue: String = record.value(for: "iconValue") {
                    category.icon = .emoji(emojiValue)
                }
            case "image":
                if let assetURL: URL = record.value(for: "iconValue") {
                    do {
                        let data = try Data(contentsOf: assetURL)
                        category.icon = .image(data)
                    } catch {
                        print("❌ 下载分类图标失败: \(error)")
                        category.icon = .emoji("📁") // 使用默认图标
                    }
                }
            case "systemImage":
                if let systemName: String = record.value(for: "iconValue") {
                    category.icon = .systemImage(systemName)
                }
            default:
                category.icon = .emoji("📁") // 使用默认图标
            }
        }
        
        return category
    }
    
    /// 同步当前分类到 CloudKit
    func syncToCloud() {
        CloudSyncManager.shared.sync(self)
    }
    
    /// 从 CloudKit 删除当前分类
    func deleteFromCloud() {
        CloudSyncManager.shared.delete(self)
    }
}

// MARK: - TransactionSubCategoryModel 的 MYRecordConvertible 实现
extension TransactionSubCategoryModel: MYRecordConvertible {
    
    /// CloudKit 记录的唯一标识符
    public var myRecordID: String {
        return id
    }
    
    /// CloudKit 记录类型
    public var myRecordType: String {
        return "TransactionSubCategory"
    }
    
    /// 根组ID - 与主分类保持一致
    public var myRootGroupID: String? {
        switch CloudSyncManager.shared.shareType {
        case .recordWithMYZone:
            return nil
        case .zone, .recordWithCustomZone:
            return "CategoryGroup" // 与主分类在同一个组中
        }
    }
    
    /// 父记录ID - 指向主分类
    public var myParentID: String? {
        switch CloudSyncManager.shared.shareType {
        case .recordWithMYZone, .recordWithCustomZone:
            return mainId
        case .zone:
            return nil
        }
    }
    
    /// 需要同步到 CloudKit 的属性
    public var myProperties: [String: MYRecordValue] {
        var properties: [String: MYRecordValue] = [
            "name": .string(name),
            "order": .int64(Int64(order)),
            "mainId": .string(mainId)
        ]
        
        // 图标处理
        switch icon {
        case .emoji(let emojiString):
            properties["iconType"] = .string("emoji")
            properties["iconValue"] = .string(emojiString)
        case .image(let imageData):
            if let tempURL = createTempFileForIcon(imageData) {
                properties["iconType"] = .string("image")
                properties["iconValue"] = .asset(tempURL)
            }
        case .systemImage(let systemName):
            properties["iconType"] = .string("systemImage")
            properties["iconValue"] = .string(systemName)
        }
        
        // 如果不使用父子关系，则添加主分类引用
        if CloudSyncManager.shared.shareType == .zone,
           let mainCategory = mainCategory {
            properties["mainCategory"] = .reference(mainCategory, deleteRule: .deleteSelf)
        }
        
        return properties
    }
    
    /// 为图标创建临时文件
    private func createTempFileForIcon(_ data: Data) -> URL? {
        let tempDir = FileManager.default.temporaryDirectory
        let fileName = "subCategoryIcon_\(id).png"
        let tempURL = tempDir.appendingPathComponent(fileName)
        
        do {
            try data.write(to: tempURL)
            return tempURL
        } catch {
            print("❌ 创建子分类图标临时文件失败: \(error)")
            return nil
        }
    }
}

// MARK: - TransactionSubCategoryModel 便利方法
extension TransactionSubCategoryModel {
    
    /// 从 CloudKit 记录创建或更新 TransactionSubCategoryModel
    static func from(record: MYSyncEngine.FetchedRecord, existing: TransactionSubCategoryModel? = nil) -> TransactionSubCategoryModel {
        let subCategory = existing ?? TransactionSubCategoryModel(
            id: record.id,
            name: "",
            icon: .emoji("📄"),
            order: 0,
            mainId: ""
        )
        
        // 更新基本属性
        subCategory.id = record.id
        subCategory.name = record.value(for: "name") ?? ""
        subCategory.order = Int(record.value(for: "order") as? Int64 ?? 0)
        subCategory.mainId = record.value(for: "mainId") ?? ""
        
        // 图标处理（与主分类相同的逻辑）
        if let iconType: String = record.value(for: "iconType") {
            switch iconType {
            case "emoji":
                if let emojiValue: String = record.value(for: "iconValue") {
                    subCategory.icon = .emoji(emojiValue)
                }
            case "image":
                if let assetURL: URL = record.value(for: "iconValue") {
                    do {
                        let data = try Data(contentsOf: assetURL)
                        subCategory.icon = .image(data)
                    } catch {
                        print("❌ 下载子分类图标失败: \(error)")
                        subCategory.icon = .emoji("📄")
                    }
                }
            case "systemImage":
                if let systemName: String = record.value(for: "iconValue") {
                    subCategory.icon = .systemImage(systemName)
                }
            default:
                subCategory.icon = .emoji("📄")
            }
        }
        
        return subCategory
    }
    
    /// 同步当前子分类到 CloudKit
    func syncToCloud() {
        CloudSyncManager.shared.sync(self)
    }
    
    /// 从 CloudKit 删除当前子分类
    func deleteFromCloud() {
        CloudSyncManager.shared.delete(self)
    }
}
