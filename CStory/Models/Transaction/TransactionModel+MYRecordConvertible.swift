//
//  TransactionModel+MYRecordConvertible.swift
//  CStory
//
//  Created by AI Assistant on 2025/8/13.
//

import Foundation
import MYCloudKit
import CloudKit

/// TransactionModel 的 MYRecordConvertible 实现
extension TransactionModel: MYRecordConvertible {
    
    /// CloudKit 记录的唯一标识符
    public var myRecordID: String {
        return id.uuidString
    }
    
    /// CloudKit 记录类型
    public var myRecordType: String {
        return "Transaction"
    }
    
    /// 根组ID - 可以按卡片或日期分组
    public var myRootGroupID: String? {
        switch CloudSyncManager.shared.shareType {
        case .recordWithMYZone:
            return nil
        case .zone, .recordWithCustomZone:
            // 可以选择按卡片分组或按月份分组
            if let fromCardId = fromCardId {
                return "TransactionGroup_\(fromCardId.uuidString)"
            } else if let toCardId = toCardId {
                return "TransactionGroup_\(toCardId.uuidString)"
            } else {
                // 按年月分组
                let formatter = DateFormatter()
                formatter.dateFormat = "yyyy-MM"
                return "TransactionGroup_\(formatter.string(from: transactionDate))"
            }
        }
    }
    
    /// 父记录ID - 交易没有父记录
    public var myParentID: String? {
        return nil
    }
    
    /// 需要同步到 CloudKit 的属性
    public var myProperties: [String: MYRecordValue] {
        var properties: [String: MYRecordValue] = [
            "transactionType": .string(transactionType.rawValue),
            "transactionAmount": .double(transactionAmount),
            "currency": .string(currency),
            "symbol": .string(symbol),
            "expenseToCardRate": .double(expenseToCardRate),
            "expenseToBaseRate": .double(expenseToBaseRate),
            "incomeToCardRate": .double(incomeToCardRate),
            "incomeToBaseRate": .double(incomeToBaseRate),
            "isStatistics": .bool(isStatistics),
            "remark": .string(remark),
            "transactionDate": .date(transactionDate),
            "createdAt": .date(createdAt),
            "updatedAt": .date(updatedAt)
        ]
        
        // 可选字段处理
        if let originalTransactionId = originalTransactionId {
            properties["originalTransactionId"] = .string(originalTransactionId.uuidString)
        }
        
        if let transactionCategoryId = transactionCategoryId {
            properties["transactionCategoryId"] = .string(transactionCategoryId)
        }
        
        if let fromCardId = fromCardId {
            properties["fromCardId"] = .string(fromCardId.uuidString)
        }
        
        if let toCardId = toCardId {
            properties["toCardId"] = .string(toCardId.uuidString)
        }
        
        if let discountAmount = discountAmount {
            properties["discountAmount"] = .double(discountAmount)
        }
        
        if let refundAmount = refundAmount {
            properties["refundAmount"] = .double(refundAmount)
        }
        
        if let originalTradId = originalTradId {
            properties["originalTradId"] = .string(originalTradId.uuidString)
        }
        
        return properties
    }
}

// MARK: - 便利方法
extension TransactionModel {
    
    /// 从 CloudKit 记录创建或更新 TransactionModel
    /// - Parameters:
    ///   - record: CloudKit 获取的记录
    ///   - existing: 现有的实例（可选）
    /// - Returns: 更新后的实例
    static func from(record: MYSyncEngine.FetchedRecord, existing: TransactionModel? = nil) -> TransactionModel {
        // 解析 UUID
        guard let recordUUID = UUID(uuidString: record.id) else {
            fatalError("无效的 Transaction UUID: \(record.id)")
        }
        
        let transaction = existing ?? TransactionModel(
            id: recordUUID,
            transactionType: .expense,
            transactionAmount: 0,
            currency: "",
            symbol: "",
            expenseToCardRate: 1.0,
            expenseToBaseRate: 1.0,
            incomeToCardRate: 1.0,
            incomeToBaseRate: 1.0,
            isStatistics: false,
            remark: "",
            transactionDate: Date(),
            createdAt: Date(),
            updatedAt: Date()
        )
        
        // 更新基本属性
        transaction.id = recordUUID
        
        // 交易类型
        if let typeString: String = record.value(for: "transactionType"),
           let type = TransactionType(rawValue: typeString) {
            transaction.transactionType = type
        }
        
        transaction.transactionAmount = record.value(for: "transactionAmount") ?? 0
        transaction.currency = record.value(for: "currency") ?? ""
        transaction.symbol = record.value(for: "symbol") ?? ""
        transaction.expenseToCardRate = record.value(for: "expenseToCardRate") ?? 1.0
        transaction.expenseToBaseRate = record.value(for: "expenseToBaseRate") ?? 1.0
        transaction.incomeToCardRate = record.value(for: "incomeToCardRate") ?? 1.0
        transaction.incomeToBaseRate = record.value(for: "incomeToBaseRate") ?? 1.0
        transaction.isStatistics = record.value(for: "isStatistics") ?? false
        transaction.remark = record.value(for: "remark") ?? ""
        
        // 日期处理
        if let transactionDate: Date = record.value(for: "transactionDate") {
            transaction.transactionDate = transactionDate
        }
        if let createdAt: Date = record.value(for: "createdAt") {
            transaction.createdAt = createdAt
        }
        if let updatedAt: Date = record.value(for: "updatedAt") {
            transaction.updatedAt = updatedAt
        }
        
        // 可选字段处理
        if let originalTransactionIdString: String = record.value(for: "originalTransactionId"),
           let originalTransactionId = UUID(uuidString: originalTransactionIdString) {
            transaction.originalTransactionId = originalTransactionId
        }
        
        if let transactionCategoryId: String = record.value(for: "transactionCategoryId") {
            transaction.transactionCategoryId = transactionCategoryId
        }
        
        if let fromCardIdString: String = record.value(for: "fromCardId"),
           let fromCardId = UUID(uuidString: fromCardIdString) {
            transaction.fromCardId = fromCardId
        }
        
        if let toCardIdString: String = record.value(for: "toCardId"),
           let toCardId = UUID(uuidString: toCardIdString) {
            transaction.toCardId = toCardId
        }
        
        if let discountAmount: Double = record.value(for: "discountAmount") {
            transaction.discountAmount = discountAmount
        }
        
        if let refundAmount: Double = record.value(for: "refundAmount") {
            transaction.refundAmount = refundAmount
        }
        
        if let originalTradIdString: String = record.value(for: "originalTradId"),
           let originalTradId = UUID(uuidString: originalTradIdString) {
            transaction.originalTradId = originalTradId
        }
        
        return transaction
    }
    
    /// 检查是否需要同步到 CloudKit
    /// - Returns: 如果需要同步返回 true
    func needsSync() -> Bool {
        // 可以添加逻辑来判断是否需要同步
        // 例如检查 updatedAt 时间戳等
        return true
    }
    
    /// 准备同步到 CloudKit
    /// 在同步前调用此方法来更新时间戳等
    func prepareForSync() {
        self.updatedAt = Date()
    }
}

// MARK: - 同步辅助方法
extension TransactionModel {
    
    /// 同步当前交易到 CloudKit
    func syncToCloud() {
        prepareForSync()
        CloudSyncManager.shared.sync(self)
    }
    
    /// 从 CloudKit 删除当前交易
    func deleteFromCloud() {
        CloudSyncManager.shared.delete(self)
    }
    
    /// 批量同步多个交易
    /// - Parameter transactions: 要同步的交易数组
    static func syncMultipleToCloud(_ transactions: [TransactionModel]) {
        for transaction in transactions {
            transaction.syncToCloud()
        }
    }
    
    /// 同步最近的交易（例如最近30天）
    /// - Parameters:
    ///   - transactions: 所有交易
    ///   - days: 最近多少天
    static func syncRecentTransactions(_ transactions: [TransactionModel], days: Int = 30) {
        let cutoffDate = Calendar.current.date(byAdding: .day, value: -days, to: Date()) ?? Date()
        let recentTransactions = transactions.filter { $0.transactionDate >= cutoffDate }
        
        print("开始同步最近 \(days) 天的 \(recentTransactions.count) 笔交易")
        syncMultipleToCloud(recentTransactions)
    }
}
