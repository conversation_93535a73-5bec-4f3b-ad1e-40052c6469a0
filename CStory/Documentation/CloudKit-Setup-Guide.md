# CStory CloudKit 同步配置指南

## 概述

本指南将帮助您配置 CStory 应用的 CloudKit 同步功能，基于 MYCloudKit 框架实现 SwiftData 与 CloudKit 的无缝同步。

## 架构说明

### 核心组件

1. **CloudSyncManager** - 主要的同步管理器
   - 管理 MYSyncEngine 实例
   - 实现 MYSyncDelegate 协议
   - 处理 SwiftData 与 CloudKit 之间的数据转换

2. **MYRecordConvertible 扩展** - 数据模型适配
   - CurrencyModel+MYRecordConvertible
   - CardModel+MYRecordConvertible
   - 更多模型的扩展（待实现）

3. **同步策略**
   - 支持三种分享模式：zone、recordWithMYZone、recordWithCustomZone
   - 依赖顺序同步：Currency → Card → Transaction → ChatMessage

## 配置步骤

### 1. CloudKit 容器配置

1. 在 Apple Developer Portal 中创建 CloudKit 容器
2. 更新 `CloudSyncManager.swift` 中的容器标识符：

```swift
self.syncEngine = MYSyncEngine(
    containerIdentifier: "iCloud.com.yourapp.identifier", // 替换为您的容器标识符
    logLevel: .debug
)
```

### 2. Xcode 项目配置

1. 在项目的 Signing & Capabilities 中添加 CloudKit 能力
2. 选择或创建 CloudKit 容器
3. 确保 iCloud 能力已启用

### 3. CloudKit Dashboard 配置

在 CloudKit Dashboard 中创建以下记录类型：

#### Currency 记录类型
- `name` (String)
- `code` (String) - 作为记录ID
- `symbol` (String)
- `rate` (Double)
- `defaultRate` (Double)
- `isBaseCurrency` (Int64) - 0/1 表示 false/true
- `isCustom` (Int64)
- `isSelected` (Int64)
- `order` (Int64)
- `createdAt` (Date/Time)
- `updatedAt` (Date/Time)

#### Card 记录类型
- `name` (String)
- `currency` (String)
- `symbol` (String)
- `balance` (Double)
- `credit` (Double)
- `isCredit` (Int64)
- `isSelected` (Int64)
- `isStatistics` (Int64)
- `order` (Int64)
- `remark` (String)
- `cover` (String)
- `bankName` (String)
- `cardNumber` (String)
- `billDay` (Int64, Optional)
- `isFixedDueDay` (Int64)
- `dueDay` (Int64, Optional)
- `bankLogo` (Asset, Optional)
- `createdAt` (Date/Time)
- `updatedAt` (Date/Time)

### 4. 权限配置

确保在 CloudKit Dashboard 中设置适当的权限：
- Private Database: Read/Write
- Shared Database: Read/Write (如果需要分享功能)

## 使用方法

### 基本同步操作

```swift
// 同步单个记录
currency.syncToCloud()
card.syncToCloud()

// 删除记录
currency.deleteFromCloud()
card.deleteFromCloud()

// 批量同步
CardModel.syncMultipleToCloud(cards)

// 手动触发同步
CloudSyncManager.shared.triggerSync()

// 手动触发获取
CloudSyncManager.shared.triggerFetch()
```

### 监听同步状态

```swift
// 在 SwiftUI 视图中监听同步状态
@State private var syncManager = CloudSyncManager.shared

var body: some View {
    VStack {
        switch syncManager.syncEngine.syncState {
        case .idle:
            Text("同步空闲")
        case .syncing:
            Text("同步中...")
        case .error(let error):
            Text("同步错误: \(error.localizedDescription)")
        }
    }
}
```

## 测试

使用 `CloudSyncTestView` 来测试同步功能：

1. 添加测试数据（货币、卡片）
2. 观察同步状态
3. 在多个设备上验证数据同步
4. 测试离线/在线场景

## 故障排除

### 常见问题

1. **同步失败**
   - 检查网络连接
   - 验证 CloudKit 容器配置
   - 查看控制台日志

2. **记录类型不匹配**
   - 确保 CloudKit Dashboard 中的记录类型与代码中的定义一致
   - 检查字段名称和类型

3. **权限问题**
   - 确保用户已登录 iCloud
   - 检查 CloudKit 权限设置

### 调试技巧

1. 启用详细日志：
```swift
self.syncEngine = MYSyncEngine(
    containerIdentifier: "your-container-id",
    logLevel: .debug // 或 .verbose
)
```

2. 使用 CloudKit Dashboard 的日志功能查看服务器端操作

3. 在模拟器和真机上分别测试

## 下一步

1. 实现剩余模型的 MYRecordConvertible 扩展：
   - TransactionMainCategoryModel
   - TransactionSubCategoryModel
   - TransactionModel
   - ChatMessageModel

2. 添加冲突解决策略

3. 实现增量同步优化

4. 添加同步进度指示器

5. 实现分享功能

## 参考资料

- [MYCloudKit GitHub](https://github.com/mufasaYC/MYCloudKit)
- [Apple CloudKit Documentation](https://developer.apple.com/documentation/cloudkit)
- [SwiftData Documentation](https://developer.apple.com/documentation/swiftdata)
